import pandas as pd
import numpy as np

# Load and parse data
df = pd.read_csv('final-24hr-8-05-24.csv')
df['TIMESTAMP'] = pd.to_datetime(df['TIME_STAMP'] + ' 2024 ' + df['Time'], format='%a %b %d %Y %H:%M:%S', errors='coerce')
df = df.sort_values('TIMESTAMP').reset_index(drop=True)

print('=== FUEL CONSUMPTION ANALYSIS ===')
print(f'Fuel range: {df["DG.FUEL"].min():.1f}% to {df["DG.FUEL"].max():.1f}%')

# Calculate fuel differences
df['FUEL_DIFF'] = df['DG.FUEL'].diff()
df['FUEL_CONSUMED'] = df['FUEL_DIFF'] * -1  # Positive when fuel decreases

print('\n=== FUEL CHANGES ===')
fuel_changes = df[df['FUEL_DIFF'] != 0]
print(f'Total fuel change events: {len(fuel_changes)}')
print('Sample fuel changes:')
print(fuel_changes[['TIMESTAMP', 'DG.FUEL', 'FUEL_DIFF', 'FUEL_CONSUMED', 'DG.POWER']].head(20))

print('\n=== FUEL CONSUMPTION WHEN GENERATOR RUNS ===')
gen_running = df[df['DG.POWER'] > 0]
print(f'Generator running periods: {len(gen_running)} intervals = {len(gen_running) * 3 / 3600:.2f} hours')

# Fuel consumption only when generator runs
fuel_when_gen_runs = gen_running[gen_running['FUEL_CONSUMED'] > 0]
print(f'Fuel consumption events when generator runs: {len(fuel_when_gen_runs)}')

if len(fuel_when_gen_runs) > 0:
    total_fuel_consumed_percent = fuel_when_gen_runs['FUEL_CONSUMED'].sum()
    total_fuel_consumed_litres = (total_fuel_consumed_percent / 100) * 75
    print(f'Total fuel consumed when generator runs: {total_fuel_consumed_percent:.2f}% = {total_fuel_consumed_litres:.2f} L')
    
    runtime_hours = len(gen_running) * 3 / 3600
    fuel_rate = total_fuel_consumed_litres / runtime_hours if runtime_hours > 0 else 0
    print(f'Realistic fuel consumption rate: {fuel_rate:.2f} L/hour')

print('\n=== TOTAL FUEL CONSUMPTION IN DATA ===')
all_fuel_consumed = df[df['FUEL_CONSUMED'] > 0]
if len(all_fuel_consumed) > 0:
    total_fuel_percent = all_fuel_consumed['FUEL_CONSUMED'].sum()
    total_fuel_litres = (total_fuel_percent / 100) * 75
    print(f'Total fuel consumed in dataset: {total_fuel_percent:.2f}% = {total_fuel_litres:.2f} L')

print('\n=== BATTERY SOC ANALYSIS ===')
print(f'Battery SoC range: {df["BAT.SOC"].min():.1f}% to {df["BAT.SOC"].max():.1f}%')
low_soc = df[df['BAT.SOC'] <= 25]
print(f'Periods with SoC ≤ 25%: {len(low_soc)} intervals = {len(low_soc) * 3 / 3600:.2f} hours')

high_soc = df[df['BAT.SOC'] >= 70]
print(f'Periods with SoC ≥ 70%: {len(high_soc)} intervals = {len(high_soc) * 3 / 3600:.2f} hours')

print('\n=== GENERATOR POWER ANALYSIS ===')
print(f'Generator power when running: {gen_running["DG.POWER"].min():.0f}W to {gen_running["DG.POWER"].max():.0f}W')
print(f'Average generator power: {gen_running["DG.POWER"].mean():.0f}W')
