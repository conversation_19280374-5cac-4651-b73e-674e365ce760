import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

# Load original data to understand patterns
print("📊 Loading original data to understand patterns...")
original_df = pd.read_csv('final-24hr-8-05-24.csv')
original_df['TIMESTAMP'] = pd.to_datetime(original_df['TIME_STAMP'] + ' 2024 ' + original_df['Time'], 
                                         format='%a %b %d %Y %H:%M:%S', errors='coerce')

print(f"Original data: {len(original_df)} rows")
print(f"Time range: {original_df['TIMESTAMP'].min()} to {original_df['TIMESTAMP'].max()}")

# Analyze patterns
print("\n🔍 Analyzing original data patterns...")
print(f"Battery SoC range: {original_df['BAT.SOC'].min():.1f}% to {original_df['BAT.SOC'].max():.1f}%")
print(f"Fuel range: {original_df['DG.FUEL'].min():.1f}% to {original_df['DG.FUEL'].max():.1f}%")
print(f"Solar voltage range: {original_df['MPPT.SOL_VOL'].min():.0f}V to {original_df['MPPT.SOL_VOL'].max():.0f}V")
print(f"Load current range: {original_df['MPPT.LOAD'].min():.1f}A to {original_df['MPPT.LOAD'].max():.1f}A")
print(f"Generator power range: {original_df['DG.POWER'].min():.0f}W to {original_df['DG.POWER'].max():.0f}W")

# System parameters
FUEL_CAPACITY = 75  # Litres
FUEL_CONSUMPTION_RATE = 2.5  # L/hour
GENERATOR_POWER_MAX = 11000  # Watts
BATTERY_CHARGE_RATE = 0.5  # %/minute when charging
BATTERY_DISCHARGE_RATE = 0.3  # %/minute when discharging
SOC_ON_THRESHOLD = 25
SOC_OFF_THRESHOLD = 70

def generate_synthetic_day(start_date, day_type="normal", weather="sunny"):
    """Generate synthetic data for one day"""
    
    # Create time series (3-second intervals for 24 hours)
    start_time = pd.to_datetime(start_date)
    time_points = []
    current_time = start_time
    
    # Generate 28800 points (24 hours * 3600 seconds / 3 seconds)
    for i in range(28800):
        time_points.append(current_time)
        current_time += timedelta(seconds=3)
    
    df = pd.DataFrame({'TIMESTAMP': time_points})
    
    # Extract time components for patterns
    df['Hour'] = df['TIMESTAMP'].dt.hour
    df['Minute'] = df['TIMESTAMP'].dt.minute
    df['DayOfWeek'] = df['TIMESTAMP'].dt.dayofweek
    
    # Initialize arrays
    n_points = len(df)
    bat_soc = np.zeros(n_points)
    dg_fuel = np.zeros(n_points)
    dg_power = np.zeros(n_points)
    mppt_sol_vol = np.zeros(n_points)
    mppt_load = np.zeros(n_points)
    program = np.zeros(n_points)
    
    # Starting conditions (with some variation)
    if day_type == "high_usage":
        initial_soc = random.uniform(45, 55)
        initial_fuel = random.uniform(40, 50)
        load_multiplier = 1.5
    elif day_type == "low_usage":
        initial_soc = random.uniform(60, 70)
        initial_fuel = random.uniform(50, 60)
        load_multiplier = 0.7
    else:  # normal
        initial_soc = random.uniform(50, 65)
        initial_fuel = random.uniform(45, 55)
        load_multiplier = 1.0
    
    current_soc = initial_soc
    current_fuel = initial_fuel
    generator_running = False
    generator_start_time = None
    
    print(f"  Generating {day_type} day with {weather} weather")
    print(f"  Starting SoC: {current_soc:.1f}%, Fuel: {current_fuel:.1f}%")
    
    for i in range(n_points):
        hour = df.loc[i, 'Hour']
        minute = df.loc[i, 'Minute']
        
        # Solar generation pattern (weather dependent)
        if weather == "sunny":
            if 6 <= hour <= 18:
                solar_base = 3000 * np.sin(np.pi * (hour - 6) / 12)
                solar_noise = random.uniform(-200, 200)
                mppt_sol_vol[i] = max(0, solar_base + solar_noise)
            else:
                mppt_sol_vol[i] = random.uniform(0, 50)
        elif weather == "cloudy":
            if 7 <= hour <= 17:
                solar_base = 1500 * np.sin(np.pi * (hour - 7) / 10)
                solar_noise = random.uniform(-300, 300)
                mppt_sol_vol[i] = max(0, solar_base + solar_noise)
            else:
                mppt_sol_vol[i] = random.uniform(0, 30)
        else:  # rainy
            mppt_sol_vol[i] = random.uniform(0, 200)
        
        # Load pattern (time of day dependent)
        if 6 <= hour <= 9:  # Morning peak
            load_base = 8 + random.uniform(-1, 2)
        elif 10 <= hour <= 16:  # Day time
            load_base = 5 + random.uniform(-1, 1)
        elif 17 <= hour <= 22:  # Evening peak
            load_base = 12 + random.uniform(-2, 3)
        else:  # Night
            load_base = 3 + random.uniform(-0.5, 1)
        
        mppt_load[i] = max(0, load_base * load_multiplier)
        
        # Program setting (load control)
        if mppt_load[i] > 10:
            program[i] = 2  # High load
        elif mppt_load[i] > 6:
            program[i] = 1  # Medium load
        else:
            program[i] = 0  # Low load
        
        # Battery SoC dynamics
        solar_charging = mppt_sol_vol[i] > 500  # Solar available for charging
        load_discharging = mppt_load[i] > 0
        
        if solar_charging and not generator_running:
            # Solar charging
            charge_rate = min(0.01, (mppt_sol_vol[i] / 3000) * 0.015)  # %/3sec
            current_soc = min(70.5, current_soc + charge_rate)
        elif load_discharging:
            # Battery discharging due to load
            discharge_rate = (mppt_load[i] / 15) * 0.008  # %/3sec
            current_soc = max(0, current_soc - discharge_rate)
        
        # Generator control logic
        if current_soc <= SOC_ON_THRESHOLD and not generator_running:
            generator_running = True
            generator_start_time = i
            print(f"    Generator started at {df.loc[i, 'TIMESTAMP']} (SoC: {current_soc:.1f}%)")
        
        if generator_running and current_soc >= SOC_OFF_THRESHOLD:
            generator_running = False
            print(f"    Generator stopped at {df.loc[i, 'TIMESTAMP']} (SoC: {current_soc:.1f}%)")
        
        # Generator operation
        if generator_running:
            # Generator charges battery
            dg_power[i] = random.uniform(8000, GENERATOR_POWER_MAX)
            charge_rate = 0.02  # %/3sec when generator runs
            current_soc = min(70.5, current_soc + charge_rate)
            
            # Fuel consumption
            fuel_consumption_per_3sec = (FUEL_CONSUMPTION_RATE / 3600) * 3 / FUEL_CAPACITY * 100
            current_fuel = max(0, current_fuel - fuel_consumption_per_3sec)
        else:
            dg_power[i] = 0
        
        # Store values
        bat_soc[i] = current_soc
        dg_fuel[i] = current_fuel
    
    # Create final dataframe with original column names
    synthetic_df = pd.DataFrame({
        'TIME_STAMP': df['TIMESTAMP'].dt.strftime('%a %b %d'),
        'Time': df['TIMESTAMP'].dt.strftime('%H:%M:%S'),
        'BAT.SOC': bat_soc,
        'Program': program.astype(int),
        'DG.FUEL': dg_fuel,
        'DG.POWER': dg_power,
        'MPPT.SOL_VOL': mppt_sol_vol,
        'MPPT.LOAD': mppt_load
    })
    
    return synthetic_df

# Generate 2 days of synthetic data
print("\n🔧 Generating synthetic data for 2 days...")

# Day 1: Normal sunny day
day1 = generate_synthetic_day("2024-05-09", day_type="normal", weather="sunny")

# Day 2: High usage cloudy day  
day2 = generate_synthetic_day("2024-05-10", day_type="high_usage", weather="cloudy")

# Combine datasets
synthetic_data = pd.concat([day1, day2], ignore_index=True)

print(f"\n✅ Generated {len(synthetic_data)} synthetic data points")
print(f"Time range: {day1['TIME_STAMP'].iloc[0]} to {day2['TIME_STAMP'].iloc[-1]}")

# Add some realistic noise and variations
print("\n🎲 Adding realistic variations...")

# Add small random variations to make it more realistic
synthetic_data['BAT.SOC'] += np.random.normal(0, 0.5, len(synthetic_data))
synthetic_data['DG.FUEL'] += np.random.normal(0, 0.2, len(synthetic_data))
synthetic_data['MPPT.SOL_VOL'] += np.random.normal(0, 50, len(synthetic_data))
synthetic_data['MPPT.LOAD'] += np.random.normal(0, 0.3, len(synthetic_data))

# Ensure realistic bounds
synthetic_data['BAT.SOC'] = np.clip(synthetic_data['BAT.SOC'], 0, 70.5)
synthetic_data['DG.FUEL'] = np.clip(synthetic_data['DG.FUEL'], 0, 100)
synthetic_data['MPPT.SOL_VOL'] = np.clip(synthetic_data['MPPT.SOL_VOL'], 0, 4000)
synthetic_data['MPPT.LOAD'] = np.clip(synthetic_data['MPPT.LOAD'], 0, 20)

# Save synthetic data
synthetic_data.to_csv('synthetic_2days_data.csv', index=False)

print(f"\n📊 Synthetic data summary:")
print(f"Battery SoC range: {synthetic_data['BAT.SOC'].min():.1f}% to {synthetic_data['BAT.SOC'].max():.1f}%")
print(f"Fuel range: {synthetic_data['DG.FUEL'].min():.1f}% to {synthetic_data['DG.FUEL'].max():.1f}%")
print(f"Solar voltage range: {synthetic_data['MPPT.SOL_VOL'].min():.0f}V to {synthetic_data['MPPT.SOL_VOL'].max():.0f}V")
print(f"Load current range: {synthetic_data['MPPT.LOAD'].min():.1f}A to {synthetic_data['MPPT.LOAD'].max():.1f}A")
print(f"Generator power range: {synthetic_data['DG.POWER'].min():.0f}W to {synthetic_data['DG.POWER'].max():.0f}W")

print(f"\n✅ Synthetic data saved as 'synthetic_2days_data.csv'")
print(f"📁 File contains {len(synthetic_data)} rows with same columns as original data")
