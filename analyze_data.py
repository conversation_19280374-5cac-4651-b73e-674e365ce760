import pandas as pd

# Load and parse data
df = pd.read_csv('final-24hr-8-05-24.csv')
df['TIMESTAMP'] = pd.to_datetime(df['TIME_STAMP'] + ' 2024 ' + df['Time'], format='%a %b %d %Y %H:%M:%S', errors='coerce')

print('=== DETAILED DATA ANALYSIS ===')
print(f'Total rows: {len(df)}')
print(f'Fuel range: {df["DG.FUEL"].min():.1f}% to {df["DG.FUEL"].max():.1f}%')
print(f'Generator power range: {df["DG.POWER"].min():.1f} to {df["DG.POWER"].max():.1f}')

print('\n=== GENERATOR POWER > 0 ===')
gen_running = df[df['DG.POWER'] > 0]
print(f'Periods with generator running: {len(gen_running)} intervals')
if len(gen_running) > 0:
    print('Sample generator running data:')
    print(gen_running[['TIMESTAMP', 'DG.FUEL', 'DG.POWER', 'BAT.SOC']].head(10))
    print(f'Total generator runtime: {len(gen_running)} intervals = {len(gen_running) * 3 / 3600:.2f} hours')

print('\n=== FUEL CONSUMPTION ANALYSIS ===')
df_sorted = df.sort_values('TIMESTAMP').reset_index(drop=True)
df_sorted['FUEL_DIFF'] = df_sorted['DG.FUEL'].diff() * -1
fuel_consumed = df_sorted[df_sorted['FUEL_DIFF'] > 0]
print(f'Periods with fuel consumption: {len(fuel_consumed)} intervals')
if len(fuel_consumed) > 0:
    total_fuel_percent = fuel_consumed['FUEL_DIFF'].sum()
    total_fuel_litres = (total_fuel_percent / 100) * 75
    print(f'Total fuel consumed: {total_fuel_percent:.2f}% = {total_fuel_litres:.2f} L')
    print('Sample fuel consumption data:')
    print(fuel_consumed[['TIMESTAMP', 'DG.FUEL', 'FUEL_DIFF', 'DG.POWER']].head(10))

print('\n=== FUEL LEVELS ≤ 25% ===')
low_fuel = df[df['DG.FUEL'] <= 25]
print(f'Periods with fuel ≤ 25%: {len(low_fuel)} intervals')
if len(low_fuel) > 0:
    print('Sample low fuel data:')
    print(low_fuel[['TIMESTAMP', 'DG.FUEL', 'DG.POWER', 'BAT.SOC']].head(10))

print('\n=== TIME PERIODS ANALYSIS ===')
# Check around 5:41 AM
morning_data = df[(df['TIMESTAMP'].dt.hour == 5) & (df['TIMESTAMP'].dt.minute >= 40)]
if len(morning_data) > 0:
    print('Data around 5:41 AM:')
    print(morning_data[['TIMESTAMP', 'DG.FUEL', 'DG.POWER', 'BAT.SOC']].head(5))

# Check around 18:30 (6:30 PM)
evening_data = df[(df['TIMESTAMP'].dt.hour == 18) & (df['TIMESTAMP'].dt.minute >= 25)]
if len(evening_data) > 0:
    print('Data around 18:30:')
    print(evening_data[['TIMESTAMP', 'DG.FUEL', 'DG.POWER', 'BAT.SOC']].head(5))
