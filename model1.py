import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.tree import DecisionTreeRegressor
from sklearn.multioutput import MultiOutputRegressor
from sklearn.metrics import r2_score, mean_absolute_error

# Load dataset
df = pd.read_csv("final-24hr-8-05-24.csv")

# Optional: If timestamp format is known, specify it here for consistency
try:
    df['TIMESTAMP'] = pd.to_datetime(df['TIME_STAMP'] + ' ' + df['Time'], format="%d-%m-%Y %H:%M:%S", errors='coerce')
except:
    df['TIMESTAMP'] = pd.to_datetime(df['TIME_STAMP'] + ' ' + df['Time'], errors='coerce')

# Debug: Show missing values count
print("Missing values before cleaning:")
print(df[['TIMESTAMP', 'BAT.SOC', 'Program', 'DG.FUEL', 'DG.POWER']].isnull().sum())

# Fill forward numerical values
df['BAT.SOC'] = df['BAT.SOC'].ffill()
df['Program'] = df['Program'].ffill()
df['DG.FUEL'] = df['DG.FUEL'].ffill()
df['DG.POWER'] = df['DG.POWER'].ffill()

# Drop rows where TIMESTAMP is still NaT
df = df.dropna(subset=['TIMESTAMP'])

if df.empty:
    raise ValueError("No valid rows left after preprocessing. Please inspect the dataset.")
else:
    print(f"✅ Dataset loaded: {len(df)} rows after cleaning.")

# Control logic: Generator ON if SoC <= 35%, OFF if SoC >= 70%
SOC_ON, SOC_OFF = 35, 70
df['GENERATOR_STATUS'] = 0
df.loc[df['BAT.SOC'] <= SOC_ON, 'GENERATOR_STATUS'] = 1
df.loc[df['BAT.SOC'] >= SOC_OFF, 'GENERATOR_STATUS'] = 0

# Calculate fuel used (% drop converted to positive values)
df['FUEL_USED'] = df['DG.FUEL'].ffill().diff().fillna(0) * -1
df['FUEL_USED'] = df['FUEL_USED'].apply(lambda x: max(x, 0))

# System info
fuel_capacity_litres = 75
battery_capacity_kwh = 18
usable_battery_kwh = 12
generator_power_kw = 10

# Inputs for prediction
feature_cols = ['BAT.SOC', 'Program', 'DG.FUEL', 'DG.POWER']
X = df[feature_cols]

# Targets: Total generator status (proxy for runtime), fuel used
y = df[['GENERATOR_STATUS', 'FUEL_USED']]

if len(X) == 0 or len(y) == 0:
    raise ValueError("Insufficient samples for training. X or y is empty.")

# Use a shallow Decision Tree model for low data
model = MultiOutputRegressor(DecisionTreeRegressor(max_depth=5, random_state=0))
model.fit(X, y)
y_pred = model.predict(X)

# Metrics
r2_gen = r2_score(y['GENERATOR_STATUS'], y_pred[:, 0])
r2_fuel = r2_score(y['FUEL_USED'], y_pred[:, 1])
mae_gen = mean_absolute_error(y['GENERATOR_STATUS'], y_pred[:, 0])
mae_fuel = mean_absolute_error(y['FUEL_USED'], y_pred[:, 1])

# Calculated totals from predictions
total_runtime_sec_pred = y_pred[:, 0].sum()
total_runtime_hrs_pred = total_runtime_sec_pred / 3600

total_fuel_pct_pred = y_pred[:, 1].sum()
total_fuel_l_pred = (total_fuel_pct_pred / 100) * fuel_capacity_litres

# Per-hour aggregation
df['Predicted_Fuel_Used'] = y_pred[:, 1]
df['Hour'] = df['TIMESTAMP'].dt.floor('H')
fuel_hourly = df.groupby('Hour')['Predicted_Fuel_Used'].sum() * fuel_capacity_litres / 100

# Output results
print("=== SHOGES ML Simulation Report (Decision Tree) ===")
print(f"🔌 Predicted Generator Runtime: {total_runtime_sec_pred:.0f} sec → {total_runtime_hrs_pred:.2f} hrs")
print(f"⛽ Predicted Fuel Usage: {total_fuel_pct_pred:.2f}% → {total_fuel_l_pred:.2f} L")
print(f"📊 Generator R²: {r2_gen:.4f}, MAE: {mae_gen:.4f}")
print(f"📉 Fuel R²: {r2_fuel:.4f}, MAE: {mae_fuel:.4f} %")

# Save output
df_output = pd.DataFrame({
    'TIMESTAMP': df['TIMESTAMP'],
    'Actual_Fuel_Used_%': y['FUEL_USED'],
    'Predicted_Fuel_Used_%': y_pred[:, 1],
    'Actual_Generator_Status': y['GENERATOR_STATUS'],
    'Predicted_Generator_Status': y_pred[:, 0]
})
df_output.to_csv("SHOGES_DT_Predictions.csv", index=False)

fuel_hourly_df = fuel_hourly.reset_index()
fuel_hourly_df.columns = ['Hour', 'Fuel_Used_Litres']
fuel_hourly_df.to_csv("SHOGES_DT_Fuel_Hourly.csv", index=False)

# Plots
plt.figure(figsize=(12, 5))
plt.plot(df['TIMESTAMP'], y['FUEL_USED'], label='Actual Fuel Usage (%)', linestyle='--', color='gray')
plt.plot(df['TIMESTAMP'], y_pred[:, 1], label='Predicted Fuel Usage (%)', color='blue')
plt.legend()
plt.title('⛽ Fuel Usage Comparison (Decision Tree)')
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig("Fuel_Usage_DT_Comparison.png")
plt.show()

plt.figure(figsize=(12, 5))
plt.plot(df['TIMESTAMP'], y['GENERATOR_STATUS'], label='Actual Generator Status', linestyle='--', color='black')
plt.plot(df['TIMESTAMP'], y_pred[:, 0], label='Predicted Generator Status', color='green')
plt.legend()
plt.title('🔌 Generator Runtime Prediction (Decision Tree)')
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig("Generator_DT_Status_Comparison.png")
plt.show()
