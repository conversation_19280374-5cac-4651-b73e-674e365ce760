import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.tree import DecisionTreeRegressor
from sklearn.ensemble import RandomForestRegressor
from sklearn.multioutput import MultiOutputRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_absolute_error, accuracy_score, mean_squared_error
import seaborn as sns
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Load dataset
df = pd.read_csv("final-24hr-8-05-24.csv")

try:
    df['TIMESTAMP'] = pd.to_datetime(df['TIME_STAMP'] + ' 2024 ' + df['Time'], format="%a %b %d %Y %H:%M:%S", errors='coerce')
except:
    # Fallback: try without specifying format
    df['TIMESTAMP'] = pd.to_datetime(df['TIME_STAMP'] + ' 2024 ' + df['Time'], errors='coerce')

print(f"Successfully parsed {df['TIMESTAMP'].notna().sum()} timestamps out of {len(df)} rows")

# Debug: Show missing values count
print("Missing values before cleaning:")
print(df[['TIMESTAMP', 'BAT.SOC', 'Program', 'DG.FUEL', 'DG.POWER']].isnull().sum())

# Fill forward numerical values
df['BAT.SOC'] = df['BAT.SOC'].ffill()
df['Program'] = df['Program'].ffill()
df['DG.FUEL'] = df['DG.FUEL'].ffill()
df['DG.POWER'] = df['DG.POWER'].ffill()

# Drop rows where TIMESTAMP is still NaT
df = df.dropna(subset=['TIMESTAMP'])

if df.empty:
    raise ValueError("No valid rows left after preprocessing. Please inspect the dataset.")
else:
    print(f"✅ Dataset loaded: {len(df)} rows after cleaning.")

# System specifications (define first)
FUEL_CAPACITY_LITRES = 75
BATTERY_CAPACITY_KWH = 18
USABLE_BATTERY_KWH = 12
GENERATOR_POWER_KW = 10

# Check fuel range in data first
fuel_min = df['DG.FUEL'].min()
fuel_max = df['DG.FUEL'].max()
print(f"📊 Fuel level range: {fuel_min:.1f}% to {fuel_max:.1f}%")

# CORRECTED Control logic based on actual data range
# Since fuel ranges from 28% to 57%, we need realistic thresholds
FUEL_ON_THRESHOLD = 25   # Generator turns ON when fuel ≤ 25% (ORIGINAL SYSTEM SPEC)
FUEL_OFF_THRESHOLD = 70  # Generator turns OFF when fuel ≥ 70% (ORIGINAL SYSTEM SPEC)
SOC_ON, SOC_OFF = 25, 70  # ACTUAL generator control: Battery SoC ≤ 25% ON, ≥ 70% OFF

print(f"� Adjusted thresholds: Generator ON ≤ {FUEL_ON_THRESHOLD}%, OFF ≥ {FUEL_OFF_THRESHOLD}%")

# CORRECTED: Generator control based on BATTERY SoC with 1-minute startup delay
df['GENERATOR_STATUS'] = 0

# Sort by timestamp to ensure proper sequential logic
df = df.sort_values('TIMESTAMP').reset_index(drop=True)

# Initialize generator status based on BATTERY SoC with startup delay
generator_on = False
startup_time = None
STARTUP_DELAY_SECONDS = 60  # 1 minute startup delay

for i in range(len(df)):
    current_soc = df.loc[i, 'BAT.SOC']
    current_time = df.loc[i, 'TIMESTAMP']

    # Generator command turns ON when Battery SoC drops to or below 35%
    if current_soc <= SOC_ON and not generator_on and startup_time is None:
        startup_time = current_time
        print(f"🔧 Generator startup initiated at {current_time} (SoC: {current_soc}%)")

    # Generator actually starts running after 1-minute delay
    if startup_time is not None and (current_time - startup_time).total_seconds() >= STARTUP_DELAY_SECONDS:
        generator_on = True
        startup_time = None
        print(f"🔌 Generator started running at {current_time}")

    # Generator turns OFF when Battery SoC reaches or exceeds 70%
    if current_soc >= SOC_OFF and generator_on:
        generator_on = False
        print(f"🛑 Generator stopped at {current_time} (SoC: {current_soc}%)")

    # Set status (1 if running, 0 if not)
    df.loc[i, 'GENERATOR_STATUS'] = 1 if generator_on else 0

print(f"🔧 Generator control: ON when Battery SoC ≤ {SOC_ON}%, OFF when SoC ≥ {SOC_OFF}% (1-min startup delay)")
print(f"⛽ Fuel consumption: Only occurs when generator is running")
print(f"📊 Generator running periods: {df['GENERATOR_STATUS'].sum()} out of {len(df)} intervals")

# CORRECTED: Calculate fuel consumption ONLY when generator is running
df['FUEL_USED_RAW'] = df['DG.FUEL'].ffill().diff().fillna(0) * -1
df['FUEL_USED_RAW'] = df['FUEL_USED_RAW'].apply(lambda x: max(x, 0))

# Fuel is consumed ONLY when generator is running
df['FUEL_USED'] = df['FUEL_USED_RAW'] * df['GENERATOR_STATUS']

# Calculate totals with proper fuel consumption analysis
total_fuel_consumed_percent = df['FUEL_USED'].sum()
total_fuel_consumed_litres = (total_fuel_consumed_percent / 100) * FUEL_CAPACITY_LITRES
total_runtime_intervals = df['GENERATOR_STATUS'].sum()
total_runtime_hours = (total_runtime_intervals * 3) / 3600  # Assuming 3-second intervals

# Calculate fuel consumption rate
if total_runtime_hours > 0:
    fuel_consumption_rate_lph = total_fuel_consumed_litres / total_runtime_hours
    fuel_consumption_rate_percent_ph = total_fuel_consumed_percent / total_runtime_hours
else:
    fuel_consumption_rate_lph = 0
    fuel_consumption_rate_percent_ph = 0

# Calculate efficiency metrics
tank_utilization = (total_fuel_consumed_litres / FUEL_CAPACITY_LITRES) * 100

print(f"⛽ Total fuel consumed: {total_fuel_consumed_percent:.2f}% = {total_fuel_consumed_litres:.2f} L")
print(f"🔌 Total generator runtime: {total_runtime_hours:.2f} hours ({total_runtime_intervals} intervals)")
print(f"📊 Fuel consumption rate: {fuel_consumption_rate_lph:.2f} L/hour ({fuel_consumption_rate_percent_ph:.2f}%/hour)")
print(f"🏭 Tank utilization: {tank_utilization:.1f}% of {FUEL_CAPACITY_LITRES}L capacity")

# Validate against actual data
actual_gen_power_intervals = len(df[df['DG.POWER'] > 0])
actual_runtime_hours = (actual_gen_power_intervals * 3) / 3600
print(f"✅ Validation - Actual generator power > 0: {actual_gen_power_intervals} intervals = {actual_runtime_hours:.2f} hours")

# System specifications already defined above

# Enhanced feature engineering
print("🔧 Creating enhanced features...")

# Time-based features
df['Hour'] = df['TIMESTAMP'].dt.hour
df['DayOfWeek'] = df['TIMESTAMP'].dt.dayofweek
df['IsWeekend'] = (df['DayOfWeek'] >= 5).astype(int)

# Solar power proxy (MPPT.SOL_VOL represents solar voltage)
df['Solar_Power_Available'] = (df['MPPT.SOL_VOL'] > df['MPPT.SOL_VOL'].quantile(0.3)).astype(int)

# Load intensity
df['Load_Intensity'] = df['MPPT.LOAD'] / df['MPPT.LOAD'].max()

# Battery state features (for reference, but not primary control)
df['Battery_Critical'] = (df['BAT.SOC'] <= 35).astype(int)
df['Battery_Full'] = (df['BAT.SOC'] >= 70).astype(int)
df['Battery_Usable_Energy'] = (df['BAT.SOC'] / 100) * USABLE_BATTERY_KWH

# CORRECTED Generator control features (primary control logic)
df['Fuel_Level_Critical'] = (df['DG.FUEL'] <= FUEL_ON_THRESHOLD).astype(int)  # This triggers generator ON
df['Fuel_Level_High'] = (df['DG.FUEL'] >= FUEL_OFF_THRESHOLD).astype(int)    # This triggers generator OFF
df['Generator_Running'] = (df['DG.POWER'] > 0).astype(int)
df['Generator_Should_Run'] = df['Fuel_Level_Critical']  # Generator should run when fuel is critical

# Enhanced input features
feature_cols = [
    'BAT.SOC', 'Program', 'DG.FUEL', 'DG.POWER', 'MPPT.SOL_VOL', 'MPPT.LOAD',
    'Hour', 'IsWeekend', 'Solar_Power_Available', 'Load_Intensity',
    'Battery_Critical', 'Battery_Full', 'Battery_Usable_Energy',
    'Fuel_Level_Critical', 'Fuel_Level_High', 'Generator_Should_Run'
]

X = df[feature_cols].fillna(0)

# Enhanced target variables
# 1. Generator runtime (in minutes per interval)
df['Generator_Runtime_Minutes'] = df['GENERATOR_STATUS'] * 3  # Assuming 3-second intervals
# 2. Fuel consumption in litres
df['Fuel_Consumed_Litres'] = (df['FUEL_USED'] / 100) * FUEL_CAPACITY_LITRES

y = df[['Generator_Runtime_Minutes', 'Fuel_Consumed_Litres']].fillna(0)

print(f"✅ Features prepared: {X.shape[1]} features, {len(X)} samples")
print(f"📊 Feature columns: {feature_cols}")

if len(X) == 0 or len(y) == 0:
    raise ValueError("Insufficient samples for training. X or y is empty.")

# Train-test split for proper evaluation
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Enhanced ML model - Random Forest for better performance
print("🤖 Training Random Forest model...")
model = MultiOutputRegressor(
    RandomForestRegressor(
        n_estimators=100,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    )
)

model.fit(X_train, y_train)
y_pred_train = model.predict(X_train)
y_pred_test = model.predict(X_test)

# Comprehensive Model Evaluation
print("📊 Evaluating model performance...")

# Training set metrics
r2_runtime_train = r2_score(y_train.iloc[:, 0], y_pred_train[:, 0])
r2_fuel_train = r2_score(y_train.iloc[:, 1], y_pred_train[:, 1])
mae_runtime_train = mean_absolute_error(y_train.iloc[:, 0], y_pred_train[:, 0])
mae_fuel_train = mean_absolute_error(y_train.iloc[:, 1], y_pred_train[:, 1])
rmse_runtime_train = np.sqrt(mean_squared_error(y_train.iloc[:, 0], y_pred_train[:, 0]))
rmse_fuel_train = np.sqrt(mean_squared_error(y_train.iloc[:, 1], y_pred_train[:, 1]))

# Test set metrics
r2_runtime_test = r2_score(y_test.iloc[:, 0], y_pred_test[:, 0])
r2_fuel_test = r2_score(y_test.iloc[:, 1], y_pred_test[:, 1])
mae_runtime_test = mean_absolute_error(y_test.iloc[:, 0], y_pred_test[:, 0])
mae_fuel_test = mean_absolute_error(y_test.iloc[:, 1], y_pred_test[:, 1])
rmse_runtime_test = np.sqrt(mean_squared_error(y_test.iloc[:, 0], y_pred_test[:, 0]))
rmse_fuel_test = np.sqrt(mean_squared_error(y_test.iloc[:, 1], y_pred_test[:, 1]))

# Predictions on full dataset for analysis
y_pred_full = model.predict(X)

# Calculate total predictions
total_runtime_minutes_pred = y_pred_full[:, 0].sum()
total_runtime_hours_pred = total_runtime_minutes_pred / 60

total_fuel_litres_pred = y_pred_full[:, 1].sum()

# Actual totals for comparison
total_runtime_minutes_actual = y.iloc[:, 0].sum()
total_runtime_hours_actual = total_runtime_minutes_actual / 60
total_fuel_litres_actual = y.iloc[:, 1].sum()

# Hourly fuel consumption analysis
df_analysis = df.copy()
df_analysis['Predicted_Runtime_Minutes'] = y_pred_full[:, 0]
df_analysis['Predicted_Fuel_Litres'] = y_pred_full[:, 1]
df_analysis['Hour_Floor'] = df_analysis['TIMESTAMP'].dt.floor('H')

# Aggregate by hour
hourly_analysis = df_analysis.groupby('Hour_Floor').agg({
    'Generator_Runtime_Minutes': 'sum',
    'Predicted_Runtime_Minutes': 'sum',
    'Fuel_Consumed_Litres': 'sum',
    'Predicted_Fuel_Litres': 'sum'
}).reset_index()

hourly_analysis.columns = ['Hour', 'Actual_Runtime_Min', 'Predicted_Runtime_Min',
                          'Actual_Fuel_L', 'Predicted_Fuel_L']

# Comprehensive Results Report
print("\n" + "="*60)
print("🏭 SHOGES ML SIMULATION REPORT (Random Forest)")
print("="*60)

print(f"\n📊 MODEL PERFORMANCE METRICS:")
print(f"{'Metric':<25} {'Train':<15} {'Test':<15}")
print("-" * 55)
print(f"{'Runtime R² Score':<25} {r2_runtime_train:<15.4f} {r2_runtime_test:<15.4f}")
print(f"{'Fuel R² Score':<25} {r2_fuel_train:<15.4f} {r2_fuel_test:<15.4f}")
print(f"{'Runtime MAE (min)':<25} {mae_runtime_train:<15.2f} {mae_runtime_test:<15.2f}")
print(f"{'Fuel MAE (L)':<25} {mae_fuel_train:<15.4f} {mae_fuel_test:<15.4f}")
print(f"{'Runtime RMSE (min)':<25} {rmse_runtime_train:<15.2f} {rmse_runtime_test:<15.2f}")
print(f"{'Fuel RMSE (L)':<25} {rmse_fuel_train:<15.4f} {rmse_fuel_test:<15.4f}")

print(f"\n🔌 GENERATOR RUNTIME PREDICTIONS:")
print(f"Actual Total Runtime:    {total_runtime_hours_actual:.2f} hours ({total_runtime_minutes_actual:.0f} minutes)")
print(f"Predicted Total Runtime: {total_runtime_hours_pred:.2f} hours ({total_runtime_minutes_pred:.0f} minutes)")
print(f"Runtime Accuracy:        {(1 - abs(total_runtime_hours_actual - total_runtime_hours_pred) / max(total_runtime_hours_actual, 0.1)) * 100:.1f}%")

print(f"\n⛽ FUEL CONSUMPTION PREDICTIONS:")
print(f"Actual Total Fuel:       {total_fuel_litres_actual:.2f} L")
print(f"Predicted Total Fuel:    {total_fuel_litres_pred:.2f} L")
print(f"Fuel Accuracy:           {(1 - abs(total_fuel_litres_actual - total_fuel_litres_pred) / max(total_fuel_litres_actual, 0.1)) * 100:.1f}%")

print(f"\n� SYSTEM SPECIFICATIONS:")
print(f"Fuel Tank Capacity:      {FUEL_CAPACITY_LITRES} L")
print(f"Battery Capacity:        {BATTERY_CAPACITY_KWH} kWh (Usable: {USABLE_BATTERY_KWH} kWh)")
print(f"Generator Power:         {GENERATOR_POWER_KW} kW")
print(f"Control Logic:           Generator ON ≤ {FUEL_ON_THRESHOLD}% fuel, OFF ≥ {FUEL_OFF_THRESHOLD}% fuel")

print(f"\n📈 ACTUAL SYSTEM PERFORMANCE:")
print(f"Total Fuel Consumed:     {total_fuel_consumed_litres:.2f} L ({total_fuel_consumed_percent:.2f}%)")
print(f"Total Generator Runtime: {total_runtime_hours:.2f} hours")
print(f"Fuel Consumption Rate:   {fuel_consumption_rate_lph:.2f} L/hour")
print(f"Tank Utilization:        {tank_utilization:.1f}% of {FUEL_CAPACITY_LITRES}L capacity")
print(f"Generator Efficiency:    {GENERATOR_POWER_KW}kW for {total_runtime_hours:.2f}h = {GENERATOR_POWER_KW * total_runtime_hours:.1f}kWh generated")

print(f"\n🔍 DATA VALIDATION:")
print(f"Model Runtime:           {total_runtime_hours:.2f} hours")
print(f"Actual Data Runtime:     {actual_runtime_hours:.2f} hours")
print(f"Runtime Match:           {'✅ Good' if abs(total_runtime_hours - actual_runtime_hours) < 0.5 else '⚠️ Check model'}")

# Save comprehensive results
print("\n💾 Saving results to CSV files...")

# Detailed predictions
df_output = pd.DataFrame({
    'TIMESTAMP': df['TIMESTAMP'],
    'Actual_Runtime_Minutes': y.iloc[:, 0],
    'Predicted_Runtime_Minutes': y_pred_full[:, 0],
    'Actual_Fuel_Litres': y.iloc[:, 1],
    'Predicted_Fuel_Litres': y_pred_full[:, 1],
    'BAT_SOC': df['BAT.SOC'],
    'Load_Program': df['Program'],
    'DG_Fuel_Level': df['DG.FUEL'],
    'DG_Power': df['DG.POWER'],
    'Solar_Voltage': df['MPPT.SOL_VOL'],
    'Load_Current': df['MPPT.LOAD']
})
df_output.to_csv("SHOGES_RF_Detailed_Predictions.csv", index=False)

# Hourly aggregated results
hourly_analysis.to_csv("SHOGES_RF_Hourly_Analysis.csv", index=False)

# Model performance metrics
metrics_df = pd.DataFrame({
    'Metric': ['Runtime_R2', 'Fuel_R2', 'Runtime_MAE', 'Fuel_MAE', 'Runtime_RMSE', 'Fuel_RMSE'],
    'Train_Score': [r2_runtime_train, r2_fuel_train, mae_runtime_train, mae_fuel_train, rmse_runtime_train, rmse_fuel_train],
    'Test_Score': [r2_runtime_test, r2_fuel_test, mae_runtime_test, mae_fuel_test, rmse_runtime_test, rmse_fuel_test]
})
metrics_df.to_csv("SHOGES_RF_Model_Metrics.csv", index=False)

print("✅ Results saved successfully!")

# Comprehensive Visualizations
print("\n📊 Creating comprehensive visualizations...")

# Set up the plotting style
plt.rcParams['figure.figsize'] = (15, 10)
plt.rcParams['font.size'] = 10

# 1. Model Performance Comparison
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

# Runtime comparison
ax1.plot(df['TIMESTAMP'], y.iloc[:, 0], label='Actual Runtime (min)', linestyle='--', color='red', alpha=0.7)
ax1.plot(df['TIMESTAMP'], y_pred_full[:, 0], label='Predicted Runtime (min)', color='blue', alpha=0.8)
ax1.set_title('🔌 Generator Runtime Prediction Comparison')
ax1.set_ylabel('Runtime (minutes)')
ax1.legend()
ax1.tick_params(axis='x', rotation=45)
ax1.grid(True, alpha=0.3)

# Fuel consumption comparison
ax2.plot(df['TIMESTAMP'], y.iloc[:, 1], label='Actual Fuel (L)', linestyle='--', color='red', alpha=0.7)
ax2.plot(df['TIMESTAMP'], y_pred_full[:, 1], label='Predicted Fuel (L)', color='green', alpha=0.8)
ax2.set_title('⛽ Fuel Consumption Prediction Comparison')
ax2.set_ylabel('Fuel Consumption (Litres)')
ax2.legend()
ax2.tick_params(axis='x', rotation=45)
ax2.grid(True, alpha=0.3)

# Battery SoC and Generator Status
ax3.plot(df['TIMESTAMP'], df['BAT.SOC'], label='Battery SoC (%)', color='orange', alpha=0.8)
ax3.axhline(y=35, color='red', linestyle='--', alpha=0.7, label='Generator ON Threshold (35%)')
ax3.axhline(y=70, color='green', linestyle='--', alpha=0.7, label='Generator OFF Threshold (70%)')
ax3.set_title('🔋 Battery State of Charge vs Control Logic')
ax3.set_ylabel('Battery SoC (%)')
ax3.legend()
ax3.tick_params(axis='x', rotation=45)
ax3.grid(True, alpha=0.3)

# Load Profile and Solar Generation
ax4.plot(df['TIMESTAMP'], df['MPPT.LOAD'], label='Load Current (A)', color='purple', alpha=0.8)
ax4_twin = ax4.twinx()
ax4_twin.plot(df['TIMESTAMP'], df['MPPT.SOL_VOL'], label='Solar Voltage (V)', color='yellow', alpha=0.8)
ax4.set_title('⚡ Load Profile and Solar Generation')
ax4.set_ylabel('Load Current (A)', color='purple')
ax4_twin.set_ylabel('Solar Voltage (V)', color='orange')
ax4.tick_params(axis='x', rotation=45)
ax4.grid(True, alpha=0.3)
ax4.legend(loc='upper left')
ax4_twin.legend(loc='upper right')

plt.tight_layout()
plt.savefig("SHOGES_RF_Comprehensive_Analysis.png", dpi=300, bbox_inches='tight')
plt.close()

# 2. Hourly Analysis
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

# Hourly runtime
ax1.bar(range(len(hourly_analysis)), hourly_analysis['Actual_Runtime_Min'],
        alpha=0.7, label='Actual Runtime', color='red', width=0.4)
ax1.bar([x + 0.4 for x in range(len(hourly_analysis))], hourly_analysis['Predicted_Runtime_Min'],
        alpha=0.7, label='Predicted Runtime', color='blue', width=0.4)
ax1.set_title('📊 Hourly Generator Runtime Comparison')
ax1.set_ylabel('Runtime (minutes)')
ax1.set_xlabel('Hour')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Hourly fuel consumption
ax2.bar(range(len(hourly_analysis)), hourly_analysis['Actual_Fuel_L'],
        alpha=0.7, label='Actual Fuel', color='red', width=0.4)
ax2.bar([x + 0.4 for x in range(len(hourly_analysis))], hourly_analysis['Predicted_Fuel_L'],
        alpha=0.7, label='Predicted Fuel', color='green', width=0.4)
ax2.set_title('⛽ Hourly Fuel Consumption Comparison')
ax2.set_ylabel('Fuel Consumption (Litres)')
ax2.set_xlabel('Hour')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig("SHOGES_RF_Hourly_Analysis.png", dpi=300, bbox_inches='tight')
plt.close()

# 3. Model Performance Metrics Visualization
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# R² Scores
metrics = ['Runtime', 'Fuel']
train_r2 = [r2_runtime_train, r2_fuel_train]
test_r2 = [r2_runtime_test, r2_fuel_test]

x = np.arange(len(metrics))
width = 0.35

ax1.bar(x - width/2, train_r2, width, label='Training', alpha=0.8, color='skyblue')
ax1.bar(x + width/2, test_r2, width, label='Testing', alpha=0.8, color='lightcoral')
ax1.set_ylabel('R² Score')
ax1.set_title('📈 Model R² Score Comparison')
ax1.set_xticks(x)
ax1.set_xticklabels(metrics)
ax1.legend()
ax1.grid(True, alpha=0.3)
ax1.set_ylim(0, 1)

# MAE Scores
train_mae = [mae_runtime_train, mae_fuel_train]
test_mae = [mae_runtime_test, mae_fuel_test]

ax2.bar(x - width/2, train_mae, width, label='Training', alpha=0.8, color='lightgreen')
ax2.bar(x + width/2, test_mae, width, label='Testing', alpha=0.8, color='orange')
ax2.set_ylabel('Mean Absolute Error')
ax2.set_title('📉 Model MAE Comparison')
ax2.set_xticks(x)
ax2.set_xticklabels(metrics)
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig("SHOGES_RF_Model_Performance.png", dpi=300, bbox_inches='tight')
plt.close()

print("✅ All visualizations created and saved successfully!")
print("\n🎯 SUMMARY:")
print(f"• Model Type: Random Forest Regressor")
print(f"• Features Used: {len(feature_cols)} features")
print(f"• Training Samples: {len(X_train)}")
print(f"• Test Samples: {len(X_test)}")
print(f"• Best Runtime R²: {max(r2_runtime_train, r2_runtime_test):.4f}")
print(f"• Best Fuel R²: {max(r2_fuel_train, r2_fuel_test):.4f}")
print(f"• Files Generated: 6 CSV files + 3 PNG visualizations")
