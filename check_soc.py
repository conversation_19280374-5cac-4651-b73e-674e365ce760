import pandas as pd

df = pd.read_csv('final-24hr-8-05-24.csv')
df['TIMESTAMP'] = pd.to_datetime(df['TIME_STAMP'] + ' 2024 ' + df['Time'], format='%a %b %d %Y %H:%M:%S', errors='coerce')

# Check data around 13:50:47
target_time = pd.to_datetime('2024-05-07 13:50:47')
time_window = df[(df['TIMESTAMP'] >= target_time - pd.Timedelta(minutes=5)) & 
                 (df['TIMESTAMP'] <= target_time + pd.Timedelta(minutes=5))]

print('=== DATA AROUND 13:50:47 ===')
print(time_window[['TIMESTAMP', 'BAT.SOC', 'DG.POWER', 'MPPT.SOL_VOL', 'DG.FUEL']].head(10))

print('\n=== BATTERY SOC STATISTICS ===')
print(f'Overall SoC range: {df["BAT.SOC"].min():.1f}% to {df["BAT.SOC"].max():.1f}%')
print(f'SoC at 13:50 timeframe: {time_window["BAT.SOC"].min():.1f}% to {time_window["BAT.SOC"].max():.1f}%')

# Check for any 0% SoC values
zero_soc = df[df['BAT.SOC'] == 0]
print(f'\n=== ZERO SOC PERIODS ===')
print(f'Periods with 0% SoC: {len(zero_soc)} intervals')
if len(zero_soc) > 0:
    print('Sample zero SoC data:')
    print(zero_soc[['TIMESTAMP', 'BAT.SOC', 'DG.POWER']].head(10))

# Check for SoC <= 25%
low_soc = df[df['BAT.SOC'] <= 25]
print(f'\n=== LOW SOC PERIODS (≤25%) ===')
print(f'Periods with SoC ≤ 25%: {len(low_soc)} intervals')
if len(low_soc) > 0:
    print('Sample low SoC data:')
    print(low_soc[['TIMESTAMP', 'BAT.SOC', 'DG.POWER']].head(10))
