import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.multioutput import MultiOutputRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
import matplotlib.pyplot as plt

print("🧪 Testing SHOGES ML Model on Synthetic Data")
print("="*60)

# Load original data for training
print("📊 Loading original training data...")
train_df = pd.read_csv('final-24hr-8-05-24.csv')
train_df['TIMESTAMP'] = pd.to_datetime(train_df['TIME_STAMP'] + ' 2024 ' + train_df['Time'], 
                                      format='%a %b %d %Y %H:%M:%S', errors='coerce')

# Load synthetic data for testing
print("📊 Loading synthetic test data...")
test_df = pd.read_csv('synthetic_2days_data.csv')
test_df['TIMESTAMP'] = pd.to_datetime(test_df['TIME_STAMP'] + ' 2024 ' + test_df['Time'], 
                                     format='%a %b %d %Y %H:%M:%S', errors='coerce')

print(f"Training data: {len(train_df)} rows (1 day)")
print(f"Test data: {len(test_df)} rows (2 days)")

# System parameters
FUEL_CAPACITY_LITRES = 75
BATTERY_CAPACITY_KWH = 18
USABLE_BATTERY_KWH = 12
GENERATOR_POWER_KW = 10
FUEL_CONSUMPTION_RATE_LPH = 2.5
SOC_ON, SOC_OFF = 25, 70

def process_data(df, dataset_name):
    """Process data with same logic as main model"""
    print(f"\n🔧 Processing {dataset_name} data...")
    
    # Clean SoC data (remove 0% readings)
    df['BAT.SOC_CLEANED'] = df['BAT.SOC'].replace(0, pd.NA)
    df['BAT.SOC_CLEANED'] = df['BAT.SOC_CLEANED'].interpolate(method='linear', limit_direction='both')
    df['BAT.SOC_CLEANED'] = df['BAT.SOC_CLEANED'].interpolate(method='nearest', limit_direction='both')
    df['BAT.SOC_CLEANED'] = df['BAT.SOC_CLEANED'].fillna(method='ffill').fillna(method='bfill')
    
    # Generator control logic
    df = df.sort_values('TIMESTAMP').reset_index(drop=True)
    df['GENERATOR_STATUS'] = 0
    
    generator_on = False
    startup_time = None
    STARTUP_DELAY_SECONDS = 60
    
    for i in range(len(df)):
        current_soc = df.loc[i, 'BAT.SOC_CLEANED']
        current_time = df.loc[i, 'TIMESTAMP']
        
        # Generator turns ON when SoC ≤ 25%
        if current_soc <= SOC_ON and not generator_on and startup_time is None:
            startup_time = current_time
        
        # Generator starts after 1-minute delay
        if startup_time is not None and (current_time - startup_time).total_seconds() >= STARTUP_DELAY_SECONDS:
            generator_on = True
            startup_time = None
        
        # Generator turns OFF when SoC ≥ 70%
        if generator_on and current_soc >= SOC_OFF:
            generator_on = False
        
        df.loc[i, 'GENERATOR_STATUS'] = 1 if generator_on else 0
    
    # Calculate realistic fuel consumption
    FUEL_CONSUMPTION_RATE_PERCENT_PH = (FUEL_CONSUMPTION_RATE_LPH / FUEL_CAPACITY_LITRES) * 100
    fuel_per_interval_percent = (FUEL_CONSUMPTION_RATE_PERCENT_PH / 3600) * 3
    
    df['FUEL_USED'] = 0.0
    df.loc[df['GENERATOR_STATUS'] == 1, 'FUEL_USED'] = fuel_per_interval_percent
    
    # Create features
    df['Hour'] = df['TIMESTAMP'].dt.hour
    df['IsWeekend'] = (df['TIMESTAMP'].dt.dayofweek >= 5).astype(int)
    
    solar_threshold = df['MPPT.SOL_VOL'].quantile(0.3)
    df['Solar_Power_Available'] = (df['MPPT.SOL_VOL'] > solar_threshold).astype(int)
    df['Load_Intensity'] = df['MPPT.LOAD'] / df['MPPT.LOAD'].max()
    
    df['Battery_Critical'] = (df['BAT.SOC_CLEANED'] <= 25).astype(int)
    df['Battery_Full'] = (df['BAT.SOC_CLEANED'] >= 70).astype(int)
    df['Battery_Usable_Energy'] = (df['BAT.SOC_CLEANED'] / 100) * USABLE_BATTERY_KWH
    
    df['Fuel_Level_Critical'] = (df['DG.FUEL'] <= 25).astype(int)
    df['Fuel_Level_High'] = (df['DG.FUEL'] >= 70).astype(int)
    df['Generator_Should_Run'] = df['Fuel_Level_Critical']
    
    # Target variables
    df['Actual_Generator_Running'] = (df['DG.POWER'] > 0).astype(int)
    df['Generator_Runtime_Minutes'] = df['Actual_Generator_Running'] * (3/60)
    df['Fuel_Consumed_Litres'] = (df['FUEL_USED'] / 100) * FUEL_CAPACITY_LITRES
    
    # Feature columns
    feature_cols = [
        'BAT.SOC_CLEANED', 'Program', 'DG.FUEL', 'DG.POWER', 'MPPT.SOL_VOL', 'MPPT.LOAD',
        'Hour', 'IsWeekend', 'Solar_Power_Available', 'Load_Intensity',
        'Battery_Critical', 'Battery_Full', 'Battery_Usable_Energy', 
        'Fuel_Level_Critical', 'Fuel_Level_High', 'Generator_Should_Run'
    ]
    
    X = df[feature_cols].fillna(0)
    y = df[['Generator_Runtime_Minutes', 'Fuel_Consumed_Litres']].fillna(0)
    
    # Calculate totals
    total_runtime_hours = df['GENERATOR_STATUS'].sum() * 3 / 3600
    total_fuel_litres = total_runtime_hours * FUEL_CONSUMPTION_RATE_LPH
    
    print(f"  Generator runtime: {total_runtime_hours:.2f} hours")
    print(f"  Fuel consumption: {total_fuel_litres:.2f} L")
    print(f"  Features: {X.shape[1]} columns, {len(X)} samples")
    
    return X, y, df, total_runtime_hours, total_fuel_litres

# Process both datasets
X_train, y_train, train_processed, train_runtime, train_fuel = process_data(train_df, "training")
X_test, y_test, test_processed, test_runtime, test_fuel = process_data(test_df, "synthetic test")

# Train model on original data
print("\n🤖 Training model on original data...")
model = MultiOutputRegressor(
    RandomForestRegressor(
        n_estimators=100,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    )
)

model.fit(X_train, y_train)

# Test on synthetic data
print("\n🧪 Testing model on synthetic data...")
y_pred_test = model.predict(X_test)

# Calculate metrics
r2_runtime = r2_score(y_test.iloc[:, 0], y_pred_test[:, 0])
r2_fuel = r2_score(y_test.iloc[:, 1], y_pred_test[:, 1])
mae_runtime = mean_absolute_error(y_test.iloc[:, 0], y_pred_test[:, 0])
mae_fuel = mean_absolute_error(y_test.iloc[:, 1], y_pred_test[:, 1])
rmse_runtime = np.sqrt(mean_squared_error(y_test.iloc[:, 0], y_pred_test[:, 0]))
rmse_fuel = np.sqrt(mean_squared_error(y_test.iloc[:, 1], y_pred_test[:, 1]))

# Calculate totals
pred_runtime_total = y_pred_test[:, 0].sum() * 60  # Convert to hours
pred_fuel_total = y_pred_test[:, 1].sum()

print("\n" + "="*60)
print("🎯 SYNTHETIC DATA TEST RESULTS")
print("="*60)

print(f"\n📊 MODEL PERFORMANCE ON SYNTHETIC DATA:")
print(f"Runtime R² Score:        {r2_runtime:.4f}")
print(f"Fuel R² Score:           {r2_fuel:.4f}")
print(f"Runtime MAE (min):       {mae_runtime:.4f}")
print(f"Fuel MAE (L):            {mae_fuel:.4f}")
print(f"Runtime RMSE (min):      {rmse_runtime:.4f}")
print(f"Fuel RMSE (L):           {rmse_fuel:.4f}")

print(f"\n🔌 GENERATOR RUNTIME COMPARISON:")
print(f"Actual Total Runtime:    {test_runtime:.2f} hours")
print(f"Predicted Total Runtime: {pred_runtime_total:.2f} hours")
runtime_accuracy = (1 - abs(test_runtime - pred_runtime_total) / max(test_runtime, 0.1)) * 100
print(f"Runtime Accuracy:        {runtime_accuracy:.1f}%")

print(f"\n⛽ FUEL CONSUMPTION COMPARISON:")
print(f"Actual Total Fuel:       {test_fuel:.2f} L")
print(f"Predicted Total Fuel:    {pred_fuel_total:.2f} L")
fuel_accuracy = (1 - abs(test_fuel - pred_fuel_total) / max(test_fuel, 0.1)) * 100
print(f"Fuel Accuracy:           {fuel_accuracy:.1f}%")

print(f"\n📈 GENERALIZATION ASSESSMENT:")
if r2_runtime > 0.8 and r2_fuel > 0.7:
    print("✅ EXCELLENT: Model generalizes well to new data")
elif r2_runtime > 0.6 and r2_fuel > 0.5:
    print("✅ GOOD: Model shows reasonable generalization")
elif r2_runtime > 0.4 and r2_fuel > 0.3:
    print("⚠️ MODERATE: Model has limited generalization")
else:
    print("❌ POOR: Model does not generalize well")

print(f"\n🔍 DATA COMPARISON:")
print(f"Training data (1 day):   Runtime {train_runtime:.2f}h, Fuel {train_fuel:.2f}L")
print(f"Test data (2 days):      Runtime {test_runtime:.2f}h, Fuel {test_fuel:.2f}L")

# Create comparison visualization
print(f"\n📊 Creating comparison plots...")
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

# Runtime comparison
ax1.plot(y_test.iloc[:1000, 0], label='Actual Runtime', alpha=0.7, color='red')
ax1.plot(y_pred_test[:1000, 0], label='Predicted Runtime', alpha=0.7, color='blue')
ax1.set_title('Runtime Prediction (First 1000 points)')
ax1.set_ylabel('Runtime (minutes)')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Fuel comparison
ax2.plot(y_test.iloc[:1000, 1], label='Actual Fuel', alpha=0.7, color='red')
ax2.plot(y_pred_test[:1000, 1], label='Predicted Fuel', alpha=0.7, color='green')
ax2.set_title('Fuel Prediction (First 1000 points)')
ax2.set_ylabel('Fuel (Litres)')
ax2.legend()
ax2.grid(True, alpha=0.3)

# Scatter plot - Runtime
ax3.scatter(y_test.iloc[:, 0], y_pred_test[:, 0], alpha=0.5, color='blue')
ax3.plot([y_test.iloc[:, 0].min(), y_test.iloc[:, 0].max()], 
         [y_test.iloc[:, 0].min(), y_test.iloc[:, 0].max()], 'r--', lw=2)
ax3.set_xlabel('Actual Runtime')
ax3.set_ylabel('Predicted Runtime')
ax3.set_title(f'Runtime Correlation (R² = {r2_runtime:.3f})')
ax3.grid(True, alpha=0.3)

# Scatter plot - Fuel
ax4.scatter(y_test.iloc[:, 1], y_pred_test[:, 1], alpha=0.5, color='green')
ax4.plot([y_test.iloc[:, 1].min(), y_test.iloc[:, 1].max()], 
         [y_test.iloc[:, 1].min(), y_test.iloc[:, 1].max()], 'r--', lw=2)
ax4.set_xlabel('Actual Fuel')
ax4.set_ylabel('Predicted Fuel')
ax4.set_title(f'Fuel Correlation (R² = {r2_fuel:.3f})')
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig("Synthetic_Data_Test_Results.png", dpi=300, bbox_inches='tight')
plt.close()

print("✅ Test completed! Results saved as 'Synthetic_Data_Test_Results.png'")
print(f"\n🎯 CONCLUSION: Model trained on 1 day, tested on 2 synthetic days")
print(f"   Runtime R²: {r2_runtime:.3f} | Fuel R²: {r2_fuel:.3f}")
